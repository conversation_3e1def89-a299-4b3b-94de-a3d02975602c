<!-- Test Cases Modal Component -->
<!--
Parameters:
- modal_id: Unique ID for the modal (required)
- test_suite: Test suite object (required)
- test_cases: List of test cases (required)
- element_suffix: Suffix for element IDs (optional, defaults to modal_id)
-->

{% set element_suffix = element_suffix or modal_id %}

<div class="modal fade" id="{{ modal_id }}" tabindex="-1" aria-labelledby="{{ modal_id }}Label" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="{{ modal_id }}Label">
                    <i class="bi bi-play-circle"></i> Test Cases - {{ test_suite.name }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Test Suite:</strong> {{ test_suite.name }} 
                    <span class="text-muted">- Select a test case to run or manage</span>
                </div>

                {% if test_cases %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test_case in test_cases %}
                            {% if test_case.active %}
                            <tr>
                                <td>{{ test_case.name }}</td>
                                <td>{{ test_case.description|truncate(50) if test_case.description else 'No description' }}</td>
                                <td>{{ test_case.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('test_case_detail', case_id=test_case.id) }}" class="btn btn-sm btn-info" title="View & Edit Steps">
                                            <i class="bi bi-eye"></i> View Steps
                                        </a>
                                        <a href="{{ url_for('run_test_case', case_id=test_case.id) }}" class="btn btn-sm btn-success" title="Run Test">
                                            <i class="bi bi-play-fill"></i> Run
                                        </a>
                                        <button onclick="deleteTestCase{{ element_suffix }}({{ test_case.id }})" class="btn btn-sm btn-danger" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test cases found for this test suite.
                    <a href="{{ url_for('test_suite_detail', suite_id=test_suite.id) }}" class="alert-link">Go to test suite details</a> to create test cases.
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> Close
                </button>
                <a href="{{ url_for('test_suite_detail', suite_id=test_suite.id) }}" class="btn btn-primary">
                    <i class="bi bi-folder-open"></i> Open Test Suite
                </a>
                <a href="{{ url_for('create_test_run', suite_id=test_suite.id) }}" class="btn btn-success">
                    <i class="bi bi-play"></i> New Test Run
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Delete test case function for this modal instance
function deleteTestCase{{ element_suffix }}(testCaseId) {
    // Show confirmation dialog
    if (confirm('Are you sure you want to delete this test case? This action cannot be undone.')) {
        fetch(`/test-case/${testCaseId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (response.ok) {
                // Reload the page to show updated list
                window.location.reload();
            } else {
                response.json().then(data => {
                    alert(`Failed to delete test case: ${data.error || 'Unknown error'}`);
                }).catch(() => {
                    alert('Failed to delete test case.');
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the test case.');
        });
    }
}
</script>
