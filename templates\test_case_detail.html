{% extends 'base.html' %}

{% block title %}{{ test_case.name }} - StreamzAI Test Generator{% endblock %}

{% block header %}Test Case: {{ test_case.name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Test Case Details</h5>
                <div>
                    <button id="editMetadataBtn" class="btn btn-sm btn-outline-primary me-2">
                        <i class="bi bi-pencil"></i> Edit
                    </button>
                    <a href="{{ url_for('test_suite_detail', suite_id=test_case.test_suite_id) }}" class="btn btn-sm btn-secondary me-2">
                        <i class="bi bi-arrow-left"></i> Back to Test Suite
                    </a>
                    <a href="{{ url_for('download_test_case', case_id=test_case.id) }}" class="btn btn-sm btn-success">
                        <i class="bi bi-download"></i> Download Test Case
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Editable Metadata Section -->
                <div id="metadataDisplay">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Name:</strong> <span id="testCaseName">{{ test_case.name }}</span></p>
                            <p>
                                <strong>Test Suite:</strong>
                                <a href="{{ url_for('test_suite_detail', suite_id=test_case.test_suite_id) }}">
                                    {{ test_case.test_suite.name }}
                                </a>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Created:</strong> {{ test_case.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <p><strong>Description:</strong></p>
                            <p id="testCaseDescription">{{ test_case.description or 'No description provided' }}</p>
                        </div>
                    </div>
                </div>

                <!-- Editable Metadata Form (Hidden by default) -->
                <div id="metadataEdit" style="display: none;">
                    <form id="metadataForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editTestCaseName" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="editTestCaseName" value="{{ test_case.name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="editTestSuite" class="form-label">Test Suite</label>
                                    <select class="form-select" id="editTestSuite" required>
                                        <!-- Will be populated via AJAX -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="editTestCaseDescription" class="form-label">Description</label>
                                    <textarea class="form-control" id="editTestCaseDescription" rows="3">{{ test_case.description or '' }}</textarea>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Save Changes
                            </button>
                            <button type="button" id="cancelEditBtn" class="btn btn-secondary">
                                <i class="bi bi-x"></i> Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Test Steps</h5>
                <div>
                    <button id="addStepBtn" class="btn btn-sm btn-outline-success me-2" data-bs-toggle="modal" data-bs-target="#addStepModal">
                        <i class="bi bi-plus"></i> Add Step
                    </button>
                    <button id="saveStepsBtn" class="btn btn-sm btn-primary me-2">
                        <i class="bi bi-save"></i> Save Changes
                    </button>
                    <button id="continueRecordingBtn" class="btn btn-sm btn-warning me-2" data-bs-toggle="modal" data-bs-target="#continueRecordingModal">
                        <i class="bi bi-record-circle"></i> Continue Recording
                    </button>
                    <a href="{{ url_for('run_test_case', case_id=test_case.id) }}" class="btn btn-sm btn-success">
                        <i class="bi bi-play-fill"></i> Run Test Case
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if action_steps %}
                <div class="table-responsive">
                    <table class="table table-hover" id="stepsTable">
                        <thead>
                            <tr>
                                <th style="width: 50px;">Order</th>
                                <th style="width: 100px;">Action</th>
                                <th>Description</th>
                                <th>Selector/Value</th>
                                <th style="width: 200px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="stepsTableBody">
                            {% for step in action_steps %}
                            <tr data-step-id="{{ step.id }}" data-step-order="{{ step.order }}">
                                <td class="step-order-cell">
                                    <div class="d-flex align-items-center">
                                        <span class="step-number">{{ loop.index }}</span>
                                        <div class="ms-2">
                                            <button class="btn btn-sm btn-outline-secondary move-up-btn" title="Move up" {{ 'disabled' if loop.first }}>
                                                <i class="bi bi-arrow-up"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary move-down-btn" title="Move down" {{ 'disabled' if loop.last }}>
                                                <i class="bi bi-arrow-down"></i>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <select class="form-select form-select-sm step-action-select" data-step-id="{{ step.id }}">
                                        <option value="click" {{ 'selected' if step.action == 'click' }}>Click</option>
                                        <option value="fill" {{ 'selected' if step.action == 'fill' }}>Fill</option>
                                        <option value="type" {{ 'selected' if step.action == 'type' }}>Type</option>
                                        <option value="navigate" {{ 'selected' if step.action == 'navigate' }}>Navigate</option>
                                        <option value="wait" {{ 'selected' if step.action == 'wait' }}>Wait</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm step-description-input"
                                           data-step-id="{{ step.id }}"
                                           value="{{ step.description or '' }}"
                                           placeholder="Step description">
                                </td>
                                <td>
                                    <div class="step-value-container">
                                        {% if step.action in ['fill', 'type'] %}
                                        <div class="mb-1">
                                            <label class="form-label form-label-sm">Selector:</label>
                                            <input type="text" class="form-control form-control-sm step-selector-input"
                                                   data-step-id="{{ step.id }}"
                                                   value="{{ step.selector or '' }}"
                                                   placeholder="CSS selector">
                                        </div>
                                        <div>
                                            <label class="form-label form-label-sm">Value:</label>
                                            <input type="text" class="form-control form-control-sm step-value-input"
                                                   data-step-id="{{ step.id }}"
                                                   value="{{ step.value or '' }}"
                                                   placeholder="Input value">
                                        </div>
                                        {% elif step.action == 'navigate' %}
                                        <div>
                                            <label class="form-label form-label-sm">URL:</label>
                                            <input type="url" class="form-control form-control-sm step-value-input"
                                                   data-step-id="{{ step.id }}"
                                                   value="{{ step.value or '' }}"
                                                   placeholder="https://example.com">
                                        </div>
                                        {% elif step.action == 'click' %}
                                        <div>
                                            <label class="form-label form-label-sm">Selector:</label>
                                            <input type="text" class="form-control form-control-sm step-selector-input"
                                                   data-step-id="{{ step.id }}"
                                                   value="{{ step.selector or '' }}"
                                                   placeholder="CSS selector">
                                        </div>
                                        {% elif step.action == 'wait' %}
                                        <div>
                                            <label class="form-label form-label-sm">Duration (ms):</label>
                                            <input type="number" class="form-control form-control-sm step-value-input"
                                                   data-step-id="{{ step.id }}"
                                                   value="{{ step.value or '1000' }}"
                                                   placeholder="1000">
                                        </div>
                                        {% else %}
                                        <input type="text" class="form-control form-control-sm step-value-input"
                                               data-step-id="{{ step.id }}"
                                               value="{{ step.value or step.selector or '' }}"
                                               placeholder="Value">
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                        <button class="btn btn-outline-warning continue-from-step-btn"
                                                data-step-order="{{ step.order }}"
                                                data-step-index="{{ loop.index0 }}"
                                                title="Continue recording from step {{ loop.index }}">
                                            <i class="bi bi-record-circle"></i> Continue
                                        </button>
                                        <button class="btn btn-outline-primary edit-step-btn"
                                                data-step-id="{{ step.id }}"
                                                title="Edit step details">
                                            <i class="bi bi-pencil"></i> Edit
                                        </button>
                                        <button class="btn btn-outline-danger delete-step-btn"
                                                data-step-id="{{ step.id }}"
                                                title="Delete this step">
                                            <i class="bi bi-trash"></i> Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test steps found for this test case.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Case Code</h5>
            </div>
            <div class="card-body">
                {% if file_content %}
                <pre class="bg-light p-3 border rounded" style="max-height: 500px; overflow: auto;">{{ file_content }}</pre>
                {% else %}
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> Test file not found or could not be read.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if test_results %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Results</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Test Run</th>
                                <th>Status</th>
                                <th>Execution Time</th>
                                <th>Error Message</th>
                                <th>Date</th>
                                <th style="width: 100px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in test_results %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('test_run_detail', run_id=result.test_run_id) }}">
                                        {{ result.test_run.name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if result.status == 'passed' else 'danger' if result.status == 'failed' else 'warning' if result.status == 'skipped' else 'secondary' }}">
                                        {{ result.status }}
                                    </span>
                                </td>
                                <td>{{ result.execution_time|round(3) if result.execution_time else 'N/A' }} s</td>
                                <td>
                                    {% if result.error_message %}
                                    <button class="btn btn-sm btn-outline-danger" type="button" data-bs-toggle="collapse" data-bs-target="#error{{ result.id }}">
                                        View Error
                                    </button>
                                    <div class="collapse mt-2" id="error{{ result.id }}">
                                        <div class="card card-body">
                                            <pre class="small text-danger mb-0">{{ result.error_message }}</pre>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">None</span>
                                    {% endif %}
                                </td>
                                <td>{{ result.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-danger delete-result-btn"
                                            data-result-id="{{ result.id }}"
                                            title="Delete this test result">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Continue Recording Modal -->
<div class="modal fade" id="continueRecordingModal" tabindex="-1" aria-labelledby="continueRecordingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="continueRecordingModalLabel">Continue Recording Test Case</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Continue Recording:</strong> This will open a browser window and execute the existing steps up to your selected point, then start recording new actions.
                </div>

                <form id="continueRecordingForm">
                    <div class="mb-3">
                        <label for="continueUrl" class="form-label">Target URL</label>
                        <input type="url" class="form-control" id="continueUrl" placeholder="https://example.com" required>
                        <div class="form-text">The URL where you want to continue recording</div>
                    </div>

                    <div class="mb-3">
                        <label for="continueFromStep" class="form-label">Continue from step:</label>
                        <select class="form-select" id="continueFromStep">
                            <option value="-1">From the beginning (before all steps)</option>
                            {% for step in action_steps %}
                            <option value="{{ loop.index0 }}">After step {{ loop.index }}: {{ step.action }} - {{ step.description or 'No description' }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">Select the point where you want to start recording new actions</div>
                    </div>
                </form>

                <div id="recordingStatus" class="mt-3" style="display: none;">
                    <div class="alert alert-warning">
                        <i class="bi bi-record-circle text-danger"></i>
                        <strong>Recording in progress...</strong> Perform your actions in the browser window.
                    </div>
                    <div class="d-flex gap-2">
                        <button id="stopContinueRecording" class="btn btn-danger">
                            <i class="bi bi-stop-circle"></i> Stop Recording
                        </button>
                        <button id="saveContinueRecording" class="btn btn-success">
                            <i class="bi bi-save"></i> Save New Steps
                        </button>
                    </div>
                </div>

                <div id="newStepsPreview" class="mt-3" style="display: none;">
                    <h6>New Steps Recorded:</h6>
                    <div id="newStepsList" class="border rounded p-2 bg-light" style="max-height: 200px; overflow-y: auto;">
                        <!-- New steps will be displayed here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="startContinueRecording" class="btn btn-warning">
                    <i class="bi bi-record-circle"></i> Start Recording
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Import Test Case Modal -->
<div class="modal fade" id="importTestCaseModal" tabindex="-1" aria-labelledby="importTestCaseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importTestCaseModalLabel">Import Test Case</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Import Test Case:</strong> Select a test case from another test suite to import its steps into this test case.
                </div>

                <form id="importTestCaseForm">
                    <div class="mb-3">
                        <label for="sourceTestSuite" class="form-label">Source Test Suite</label>
                        <select class="form-select" id="sourceTestSuite" required>
                            <option value="">Select a test suite...</option>
                            <!-- Will be populated via AJAX -->
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="sourceTestCase" class="form-label">Source Test Case</label>
                        <select class="form-select" id="sourceTestCase" required disabled>
                            <option value="">Select a test case...</option>
                            <!-- Will be populated via AJAX -->
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="importPosition" class="form-label">Import Position</label>
                        <select class="form-select" id="importPosition">
                            <option value="beginning">At the beginning</option>
                            <option value="end" selected>At the end</option>
                            <option value="replace">Replace all existing steps</option>
                        </select>
                        <div class="form-text">Choose where to insert the imported steps</div>
                    </div>

                    <div id="importPreview" class="mt-3" style="display: none;">
                        <h6>Steps to Import:</h6>
                        <div id="importStepsList" class="border rounded p-2 bg-light" style="max-height: 200px; overflow-y: auto;">
                            <!-- Import preview will be displayed here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="previewImportBtn" class="btn btn-info">
                    <i class="bi bi-eye"></i> Preview
                </button>
                <button type="button" id="confirmImportBtn" class="btn btn-success" disabled>
                    <i class="bi bi-box-arrow-in-down"></i> Import Steps
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Step Modal -->
<div class="modal fade" id="addStepModal" tabindex="-1" aria-labelledby="addStepModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addStepModalLabel">Add New Step</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addStepForm">
                    <div class="mb-3">
                        <label for="newStepAction" class="form-label">Action</label>
                        <select class="form-select" id="newStepAction" required>
                            <option value="">Select action...</option>
                            <option value="click">Click</option>
                            <option value="fill">Fill</option>
                            <option value="type">Type</option>
                            <option value="navigate">Navigate</option>
                            <option value="wait">Wait</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="newStepDescription" class="form-label">Description</label>
                        <input type="text" class="form-control" id="newStepDescription" placeholder="Step description">
                    </div>

                    <div class="mb-3" id="newStepSelectorGroup" style="display: none;">
                        <label for="newStepSelector" class="form-label">CSS Selector</label>
                        <input type="text" class="form-control" id="newStepSelector" placeholder="e.g., #button-id, .class-name">
                    </div>

                    <div class="mb-3" id="newStepValueGroup" style="display: none;">
                        <label for="newStepValue" class="form-label">Value</label>
                        <input type="text" class="form-control" id="newStepValue" placeholder="Value">
                    </div>

                    <div class="mb-3">
                        <label for="newStepPosition" class="form-label">Insert Position</label>
                        <select class="form-select" id="newStepPosition">
                            <option value="end" selected>At the end</option>
                            <option value="beginning">At the beginning</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="confirmAddStepBtn" class="btn btn-success">
                    <i class="bi bi-plus"></i> Add Step
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Element references
        const saveStepsBtn = document.getElementById('saveStepsBtn');
        const stepInputs = document.querySelectorAll('.step-value-input');
        const continueFromStepBtns = document.querySelectorAll('.continue-from-step-btn');
        const startContinueRecordingBtn = document.getElementById('startContinueRecording');
        const stopContinueRecordingBtn = document.getElementById('stopContinueRecording');
        const saveContinueRecordingBtn = document.getElementById('saveContinueRecording');
        const continueFromStepSelect = document.getElementById('continueFromStep');

        // New element references
        const editMetadataBtn = document.getElementById('editMetadataBtn');
        const metadataDisplay = document.getElementById('metadataDisplay');
        const metadataEdit = document.getElementById('metadataEdit');
        const metadataForm = document.getElementById('metadataForm');
        const cancelEditBtn = document.getElementById('cancelEditBtn');

        // Import functionality
        const importTestCaseBtn = document.getElementById('importTestCaseBtn');
        const sourceTestSuite = document.getElementById('sourceTestSuite');
        const sourceTestCase = document.getElementById('sourceTestCase');
        const previewImportBtn = document.getElementById('previewImportBtn');
        const confirmImportBtn = document.getElementById('confirmImportBtn');

        // Add step functionality
        const addStepBtn = document.getElementById('addStepBtn');
        const newStepAction = document.getElementById('newStepAction');
        const confirmAddStepBtn = document.getElementById('confirmAddStepBtn');

        let recordingEventSource = null;
        let isRecording = false;

        // Enhanced save changes functionality
        if (saveStepsBtn) {
            saveStepsBtn.addEventListener('click', function() {
                const steps = [];

                // Collect all step data including new fields
                document.querySelectorAll('#stepsTableBody tr').forEach(row => {
                    const stepId = row.dataset.stepId;
                    if (stepId) {
                        const stepData = {
                            id: stepId,
                            action: row.querySelector('.step-action-select').value,
                            description: row.querySelector('.step-description-input').value,
                            selector: row.querySelector('.step-selector-input')?.value || '',
                            value: row.querySelector('.step-value-input')?.value || ''
                        };
                        steps.push(stepData);
                    }
                });

                // Send to server
                fetch('/api/test-case/{{ test_case.id }}/update-steps-enhanced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ steps: steps })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        showSuccessMessage('Test steps updated successfully!');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showErrorMessage('Error: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showErrorMessage('Error saving changes: ' + error.message);
                });
            });
        }

        // Edit metadata functionality
        if (editMetadataBtn) {
            editMetadataBtn.addEventListener('click', function() {
                // Load test suites for selection
                loadTestSuitesForEdit();
                metadataDisplay.style.display = 'none';
                metadataEdit.style.display = 'block';
            });
        }

        if (cancelEditBtn) {
            cancelEditBtn.addEventListener('click', function() {
                metadataDisplay.style.display = 'block';
                metadataEdit.style.display = 'none';
            });
        }

        if (metadataForm) {
            metadataForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = {
                    name: document.getElementById('editTestCaseName').value,
                    description: document.getElementById('editTestCaseDescription').value,
                    test_suite_id: document.getElementById('editTestSuite').value
                };

                fetch('/api/test-case/{{ test_case.id }}/update-metadata', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Update display
                        document.getElementById('testCaseName').textContent = formData.name;
                        document.getElementById('testCaseDescription').textContent = formData.description || 'No description provided';

                        metadataDisplay.style.display = 'block';
                        metadataEdit.style.display = 'none';
                        showSuccessMessage('Test case metadata updated successfully!');
                    } else {
                        showErrorMessage('Error: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showErrorMessage('Error updating metadata: ' + error.message);
                });
            });
        }

        // Continue from step buttons
        continueFromStepBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const stepIndex = this.dataset.stepIndex;
                continueFromStepSelect.value = stepIndex;
                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('continueRecordingModal'));
                modal.show();
            });
        });

        // Start continue recording
        if (startContinueRecordingBtn) {
            startContinueRecordingBtn.addEventListener('click', function() {
                const url = document.getElementById('continueUrl').value;
                const continueFromStep = parseInt(continueFromStepSelect.value);

                if (!url) {
                    alert('Please enter a target URL');
                    return;
                }

                // Start continuation recording
                fetch('/api/record/continue', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        test_case_id: {{ test_case.id }},
                        continue_from_step: continueFromStep,
                        url: url
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Show recording status
                        document.getElementById('recordingStatus').style.display = 'block';
                        startContinueRecordingBtn.style.display = 'none';
                        isRecording = true;

                        // Start listening for new actions
                        startListeningForActions();
                    } else {
                        alert('Error starting recording: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error starting recording: ' + error.message);
                });
            });
        }

        // Stop continue recording
        if (stopContinueRecordingBtn) {
            stopContinueRecordingBtn.addEventListener('click', function() {
                fetch('/api/record/stop', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    isRecording = false;
                    if (recordingEventSource) {
                        recordingEventSource.close();
                    }
                    document.getElementById('newStepsPreview').style.display = 'block';
                })
                .catch(error => {
                    console.error('Error stopping recording:', error);
                });
            });
        }

        // Save continue recording
        if (saveContinueRecordingBtn) {
            saveContinueRecordingBtn.addEventListener('click', function() {
                fetch('/api/record/continue/save', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        alert('New steps saved successfully! Added ' + data.new_steps_count + ' new steps.');
                        // Reload the page to show updated steps
                        window.location.reload();
                    } else {
                        alert('Error saving steps: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error saving steps: ' + error.message);
                });
            });
        }

        function startListeningForActions() {
            recordingEventSource = new EventSource('/api/record/stream');
            recordingEventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'action') {
                    addNewStepToPreview(data.data);
                }
            };
        }

        function addNewStepToPreview(action) {
            const stepsList = document.getElementById('newStepsList');
            const stepDiv = document.createElement('div');
            stepDiv.className = 'border-bottom pb-1 mb-1';
            stepDiv.innerHTML = `
                <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                <strong>${action.action}</strong>
                ${action.selector ? `<code class="ms-2">${action.selector}</code>` : ''}
                ${action.value ? `<span class="ms-2 text-success">"${action.value}"</span>` : ''}
            `;
            stepsList.appendChild(stepDiv);
            stepsList.scrollTop = stepsList.scrollHeight;
        }

        // Utility functions for notifications
        function showSuccessMessage(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            setTimeout(() => alert.remove(), 5000);
        }

        function showErrorMessage(message) {
            const alert = document.createElement('div');
            alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
            alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);
            setTimeout(() => alert.remove(), 8000);
        }

        // Initialize enhanced functionality
        initializeStepManagement();
        initializeImportFunctionality();
        initializeAddStepFunctionality();
        initializeTestResultDeletion();

        function initializeStepManagement() {
            // Step reordering
            document.addEventListener('click', function(e) {
                if (e.target.closest('.move-up-btn')) {
                    const row = e.target.closest('tr');
                    const prevRow = row.previousElementSibling;
                    if (prevRow) {
                        row.parentNode.insertBefore(row, prevRow);
                        updateStepNumbers();
                    }
                }

                if (e.target.closest('.move-down-btn')) {
                    const row = e.target.closest('tr');
                    const nextRow = row.nextElementSibling;
                    if (nextRow) {
                        row.parentNode.insertBefore(nextRow, row);
                        updateStepNumbers();
                    }
                }

                // Delete step
                if (e.target.closest('.delete-step-btn')) {
                    const stepId = e.target.closest('.delete-step-btn').dataset.stepId;
                    if (confirm('Are you sure you want to delete this step?')) {
                        deleteStep(stepId);
                    }
                }
            });

            // Dynamic form updates based on action type
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('step-action-select')) {
                    updateStepValueContainer(e.target);
                }
            });
        }

        function updateStepNumbers() {
            document.querySelectorAll('#stepsTableBody tr').forEach((row, index) => {
                const stepNumber = row.querySelector('.step-number');
                if (stepNumber) {
                    stepNumber.textContent = index + 1;
                }
            });
        }

        function updateStepValueContainer(selectElement) {
            const row = selectElement.closest('tr');
            const container = row.querySelector('.step-value-container');
            const action = selectElement.value;
            const stepId = selectElement.dataset.stepId;

            let html = '';
            switch (action) {
                case 'fill':
                case 'type':
                    html = `
                        <div class="mb-1">
                            <label class="form-label form-label-sm">Selector:</label>
                            <input type="text" class="form-control form-control-sm step-selector-input"
                                   data-step-id="${stepId}" placeholder="CSS selector">
                        </div>
                        <div>
                            <label class="form-label form-label-sm">Value:</label>
                            <input type="text" class="form-control form-control-sm step-value-input"
                                   data-step-id="${stepId}" placeholder="Input value">
                        </div>
                    `;
                    break;
                case 'click':
                    html = `
                        <div>
                            <label class="form-label form-label-sm">Selector:</label>
                            <input type="text" class="form-control form-control-sm step-selector-input"
                                   data-step-id="${stepId}" placeholder="CSS selector">
                        </div>
                    `;
                    break;
                case 'navigate':
                    html = `
                        <div>
                            <label class="form-label form-label-sm">URL:</label>
                            <input type="url" class="form-control form-control-sm step-value-input"
                                   data-step-id="${stepId}" placeholder="https://example.com">
                        </div>
                    `;
                    break;
                case 'wait':
                    html = `
                        <div>
                            <label class="form-label form-label-sm">Duration (ms):</label>
                            <input type="number" class="form-control form-control-sm step-value-input"
                                   data-step-id="${stepId}" placeholder="1000">
                        </div>
                    `;
                    break;
                default:
                    html = `
                        <input type="text" class="form-control form-control-sm step-value-input"
                               data-step-id="${stepId}" placeholder="Value">
                    `;
            }
            container.innerHTML = html;
        }

        function deleteStep(stepId) {
            fetch(`/api/test-case/{{ test_case.id }}/step/${stepId}/delete`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.querySelector(`tr[data-step-id="${stepId}"]`).remove();
                    updateStepNumbers();
                    showSuccessMessage('Step deleted successfully!');
                } else {
                    showErrorMessage('Error deleting step: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorMessage('Error deleting step: ' + error.message);
            });
        }

        function initializeImportFunctionality() {
            // Load test suites when modal opens
            if (importTestCaseBtn) {
                importTestCaseBtn.addEventListener('click', function() {
                    loadTestSuites();
                });
            }

            // Load test cases when suite is selected
            if (sourceTestSuite) {
                sourceTestSuite.addEventListener('change', function() {
                    const suiteId = this.value;
                    if (suiteId) {
                        loadTestCases(suiteId);
                    } else {
                        sourceTestCase.disabled = true;
                        sourceTestCase.innerHTML = '<option value="">Select a test case...</option>';
                    }
                });
            }

            // Preview import
            if (previewImportBtn) {
                previewImportBtn.addEventListener('click', function() {
                    const testCaseId = sourceTestCase.value;
                    if (testCaseId) {
                        previewImport(testCaseId);
                    }
                });
            }

            // Confirm import
            if (confirmImportBtn) {
                confirmImportBtn.addEventListener('click', function() {
                    const testCaseId = sourceTestCase.value;
                    const position = document.getElementById('importPosition').value;
                    if (testCaseId) {
                        importTestCase(testCaseId, position);
                    }
                });
            }
        }

        function loadTestSuites() {
            fetch('/api/test-suites')
            .then(response => response.json())
            .then(data => {
                sourceTestSuite.innerHTML = '<option value="">Select a test suite...</option>';
                data.test_suites.forEach(suite => {
                    if (suite.id !== {{ test_case.test_suite_id }}) { // Exclude current suite
                        const option = document.createElement('option');
                        option.value = suite.id;
                        option.textContent = suite.name;
                        sourceTestSuite.appendChild(option);
                    }
                });
            })
            .catch(error => {
                console.error('Error loading test suites:', error);
                showErrorMessage('Error loading test suites');
            });
        }

        function loadTestCases(suiteId) {
            fetch(`/api/test-suite/${suiteId}/test-cases`)
            .then(response => response.json())
            .then(data => {
                sourceTestCase.innerHTML = '<option value="">Select a test case...</option>';
                data.test_cases.forEach(testCase => {
                    const option = document.createElement('option');
                    option.value = testCase.id;
                    option.textContent = testCase.name;
                    sourceTestCase.appendChild(option);
                });
                sourceTestCase.disabled = false;
            })
            .catch(error => {
                console.error('Error loading test cases:', error);
                showErrorMessage('Error loading test cases');
            });
        }

        function previewImport(testCaseId) {
            fetch(`/api/test-case/${testCaseId}/steps`)
            .then(response => response.json())
            .then(data => {
                const importStepsList = document.getElementById('importStepsList');
                importStepsList.innerHTML = '';

                data.steps.forEach((step, index) => {
                    const stepDiv = document.createElement('div');
                    stepDiv.className = 'border-bottom pb-1 mb-1';
                    stepDiv.innerHTML = `
                        <span class="badge bg-secondary me-2">${index + 1}</span>
                        <span class="badge bg-primary me-2">${step.action}</span>
                        <span>${step.description || 'No description'}</span>
                        ${step.selector ? `<code class="ms-2">${step.selector}</code>` : ''}
                        ${step.value ? `<span class="ms-2 text-success">"${step.value}"</span>` : ''}
                    `;
                    importStepsList.appendChild(stepDiv);
                });

                document.getElementById('importPreview').style.display = 'block';
                confirmImportBtn.disabled = false;
            })
            .catch(error => {
                console.error('Error previewing import:', error);
                showErrorMessage('Error previewing import');
            });
        }

        function importTestCase(testCaseId, position) {
            fetch(`/api/test-case/{{ test_case.id }}/import`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    source_test_case_id: testCaseId,
                    position: position
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showSuccessMessage(`Successfully imported ${data.imported_steps_count} steps!`);
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    showErrorMessage('Error importing test case: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error importing test case:', error);
                showErrorMessage('Error importing test case: ' + error.message);
            });
        }

        function initializeAddStepFunctionality() {
            // Show/hide fields based on action type
            if (newStepAction) {
                newStepAction.addEventListener('change', function() {
                    const action = this.value;
                    const selectorGroup = document.getElementById('newStepSelectorGroup');
                    const valueGroup = document.getElementById('newStepValueGroup');
                    const valueInput = document.getElementById('newStepValue');

                    // Reset visibility
                    selectorGroup.style.display = 'none';
                    valueGroup.style.display = 'none';

                    switch (action) {
                        case 'click':
                            selectorGroup.style.display = 'block';
                            break;
                        case 'fill':
                        case 'type':
                            selectorGroup.style.display = 'block';
                            valueGroup.style.display = 'block';
                            valueInput.placeholder = 'Input value';
                            valueInput.type = 'text';
                            break;
                        case 'navigate':
                            valueGroup.style.display = 'block';
                            valueInput.placeholder = 'https://example.com';
                            valueInput.type = 'url';
                            break;
                        case 'wait':
                            valueGroup.style.display = 'block';
                            valueInput.placeholder = '1000';
                            valueInput.type = 'number';
                            break;
                    }
                });
            }

            // Add step confirmation
            if (confirmAddStepBtn) {
                confirmAddStepBtn.addEventListener('click', function() {
                    const formData = {
                        action: document.getElementById('newStepAction').value,
                        description: document.getElementById('newStepDescription').value,
                        selector: document.getElementById('newStepSelector').value,
                        value: document.getElementById('newStepValue').value,
                        position: document.getElementById('newStepPosition').value
                    };

                    if (!formData.action) {
                        showErrorMessage('Please select an action');
                        return;
                    }

                    fetch(`/api/test-case/{{ test_case.id }}/add-step`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            showSuccessMessage('Step added successfully!');
                            setTimeout(() => window.location.reload(), 1000);
                        } else {
                            showErrorMessage('Error adding step: ' + (data.error || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        console.error('Error adding step:', error);
                        showErrorMessage('Error adding step: ' + error.message);
                    });
                });
            }
        }

        // Load test suites for editing
        function loadTestSuitesForEdit() {
            fetch('/api/test-suites')
                .then(response => response.json())
                .then(data => {
                    const testSuiteSelect = document.getElementById('editTestSuite');
                    testSuiteSelect.innerHTML = '';

                    data.test_suites.forEach(suite => {
                        const option = document.createElement('option');
                        option.value = suite.id;
                        option.textContent = `${suite.name} (${suite.project_name})`;
                        option.selected = suite.id === {{ test_case.test_suite_id }};
                        testSuiteSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading test suites:', error);
                    showErrorMessage('Failed to load test suites');
                });
        }

        // Initialize test result deletion functionality
        function initializeTestResultDeletion() {
            document.addEventListener('click', function(e) {
                if (e.target.closest('.delete-result-btn')) {
                    const btn = e.target.closest('.delete-result-btn');
                    const resultId = btn.getAttribute('data-result-id');

                    if (confirm('Are you sure you want to permanently delete this test result? This action cannot be undone.')) {
                        deleteTestResult(resultId);
                    }
                }
            });
        }

        // Delete test result (hard delete)
        function deleteTestResult(resultId) {
            fetch(`/api/test-result/${resultId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showSuccessMessage('Test result deleted successfully!');
                    // Remove the row from the table
                    const row = document.querySelector(`[data-result-id="${resultId}"]`).closest('tr');
                    if (row) {
                        row.remove();
                    }
                } else {
                    showErrorMessage('Error deleting test result: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error deleting test result:', error);
                showErrorMessage('Error deleting test result: ' + error.message);
            });
        }
    });
</script>
{% endblock %}