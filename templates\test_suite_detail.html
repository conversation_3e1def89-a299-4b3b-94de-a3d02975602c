{% extends 'base.html' %}

{% block title %}{{ test_suite.name }} - StreamzAI Test Generator{% endblock %}

{% block header %}Test Suite: {{ test_suite.name }}{% endblock %}

{% block head %}
<!-- Add recorder.js script -->
<script src="{{ url_for('static', filename='js/recorder.js') }}"></script>
<!-- Add test runner scripts and styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/test-runner.css') }}">
<script src="{{ url_for('static', filename='js/test-runner.js') }}" defer></script>
<!-- Add import test case functionality -->
<script src="{{ url_for('static', filename='js/import-test-case.js') }}" defer></script>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Test Suite Details</h5>
                <div>
                    <button type="button"
                            class="btn btn-sm btn-outline-info me-2"
                            data-bs-toggle="modal"
                            data-bs-target="#recordModal{{ test_suite.id }}"
                            title="Record Test Case">
                        <i class="bi bi-record-circle"></i> Record Test Case
                    </button>
                    <button type="button"
                            class="btn btn-sm btn-outline-secondary me-2"
                            data-bs-toggle="modal"
                            data-bs-target="#importTestCaseModal{{ test_suite.id }}"
                            title="Import Test Case">
                        <i class="bi bi-box-arrow-in-down"></i> Import Test Case
                    </button>
                    <a href="{{ url_for('edit_test_suite', suite_id=test_suite.id) }}" class="btn btn-sm btn-primary me-2">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                    <a href="{{ url_for('create_test_run', suite_id=test_suite.id) }}" class="btn btn-sm btn-success">
                        <i class="bi bi-play"></i> New Test Run
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> {{ test_suite.name }}</p>
                        <p>
                            <strong>Project:</strong>
                            <a href="{{ url_for('project_detail', project_id=test_suite.project.id) }}">
                                {{ test_suite.project.name }}
                            </a>
                        </p>
                        <p><strong>Created:</strong> {{ test_suite.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        <p><strong>Last Updated:</strong> {{ test_suite.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Description:</strong></p>
                        <p>{{ test_suite.description or 'No description provided.' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Cases</h5>
            </div>
            <div class="card-body">
                {% if test_cases %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test_case in test_cases %}
                            {% if test_case.active %}
                            <tr>
                                <td>{{ test_case.name }}</td>
                                <td>{{ test_case.description|truncate(50) if test_case.description else 'No description' }}</td>
                                <td>{{ test_case.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ url_for('test_case_detail', case_id=test_case.id) }}" class="btn btn-sm btn-info" title="View & Edit Steps">
                                            <i class="bi bi-eye"></i> View Steps
                                        </a>
                                        <a href="{{ url_for('run_test_case', case_id=test_case.id) }}" class="btn btn-sm btn-success" title="Run Test">
                                            <i class="bi bi-play-fill"></i> Run
                                        </a>
                                        <button onclick="deleteTestCase({{ test_case.id }})" class="btn btn-sm btn-danger" title="Delete">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test cases found for this test suite.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Test Runs</h5>
                <a href="{{ url_for('create_test_run', suite_id=test_suite.id) }}" class="btn btn-sm btn-success">
                    <i class="bi bi-play"></i> New Test Run
                </a>
            </div>
            <div class="card-body">
                {% if test_runs %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Start Time</th>
                                <th>End Time</th>
                                <th>Duration</th>
                                <th>Results</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test_run in test_runs %}
                            {% if test_run.active %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('test_run_detail', run_id=test_run.id) }}">
                                        {{ test_run.name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if test_run.status == 'completed' else 'warning' if test_run.status == 'running' else 'danger' if test_run.status == 'failed' else 'secondary' }}">
                                        {{ test_run.status }}
                                    </span>
                                </td>
                                <td>{{ test_run.start_time.strftime('%Y-%m-%d %H:%M:%S') if test_run.start_time else 'Not started' }}</td>
                                <td>{{ test_run.end_time.strftime('%Y-%m-%d %H:%M:%S') if test_run.end_time else 'Not completed' }}</td>
                                <td>
                                    {% if test_run.start_time and test_run.end_time %}
                                    {{ (test_run.end_time - test_run.start_time).total_seconds()|round(2) }} seconds
                                    {% else %}
                                    N/A
                                    {% endif %}
                                </td>
                                <td>
                                    {% set passed = test_run.test_results|selectattr('status', 'equalto', 'passed')|selectattr('active', 'equalto', true)|list|length %}
                                    {% set failed = test_run.test_results|selectattr('status', 'equalto', 'failed')|selectattr('active', 'equalto', true)|list|length %}
                                    {% set skipped = test_run.test_results|selectattr('status', 'equalto', 'skipped')|selectattr('active', 'equalto', true)|list|length %}
                                    {% set error = test_run.test_results|selectattr('status', 'equalto', 'error')|selectattr('active', 'equalto', true)|list|length %}
                                    {% set total = test_run.test_results|selectattr('active', 'equalto', true)|list|length %}

                                    {% if total > 0 %}
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1" style="height: 10px;">
                                            {% set passed_percent = (passed / total * 100)|round|int %}
                                            {% set failed_percent = (failed / total * 100)|round|int %}
                                            {% set error_percent = (error / total * 100)|round|int %}
                                            {% set skipped_percent = (skipped / total * 100)|round|int %}

                                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ passed_percent }}%" title="Passed: {{ passed }}"></div>
                                            <div class="progress-bar bg-danger" role="progressbar" style="width: {{ failed_percent }}%" title="Failed: {{ failed }}"></div>
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: {{ error_percent }}%" title="Error: {{ error }}"></div>
                                            <div class="progress-bar bg-secondary" role="progressbar" style="width: {{ skipped_percent }}%" title="Skipped: {{ skipped }}"></div>
                                        </div>
                                        <span class="ms-2">
                                            {{ passed }}/{{ total }}
                                        </span>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">No results</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('test_run_detail', run_id=test_run.id) }}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test runs found for this test suite.
                    <a href="{{ url_for('create_test_run', suite_id=test_suite.id) }}" class="alert-link">Create a new test run</a>.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recording Modal -->
{% set modal_id = "recordModal" + test_suite.id|string %}
{% set element_suffix = test_suite.id|string %}
{% include 'partials/record_modal.html' %}

<!-- Import Test Case Modal -->
{% set import_modal_id = "importTestCaseModal" + test_suite.id|string %}
{% set import_element_suffix = test_suite.id|string %}
{% include 'partials/import_modal.html' %}


{% endblock %}

{% block extra_js %}
<!-- Initialize tooltips and recording functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Setup recording functionality for the test suite
    setupRecorder("{{ test_suite.id }}", {});

    // Setup import functionality
    setupImportTestCase("{{ test_suite.id }}", {{ test_suite.id }});
});
</script>
{% endblock %}

<script>
// Run test case functionality now uses direct links to run_test_case page

function deleteTestCase(testCaseId) {
    // Show confirmation dialog
    if (confirm('Are you sure you want to delete this test case? This action cannot be undone.')) {
        fetch(`/test-case/${testCaseId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (response.ok) {
                // Reload the page to show updated list
                window.location.reload();
            } else {
                response.json().then(data => {
                    alert(`Failed to delete test case: ${data.error || 'Unknown error'}`);
                }).catch(() => {
                    alert('Failed to delete test case.');
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the test case.');
        });
    }
}


</script>

<!-- Toast for loading indicator -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="loadingToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Running Test</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                <span>Executing test case...</span>
            </div>
        </div>
    </div>
</div>
