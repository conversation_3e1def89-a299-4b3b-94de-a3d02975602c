{% extends 'base.html' %}

{% block title %}{{ project.name }} - StreamzAI Test Generator{% endblock %}

{% block header %}Project: {{ project.name }}{% endblock %}

{% block head %}
<!-- Add test cases modal functionality -->
<script src="{{ url_for('static', filename='js/test-cases-modal.js') }}"></script>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });

        // Initialize project delete modal
        var deleteProjectModal = document.getElementById('deleteProjectModal')
        if (deleteProjectModal) {
            new bootstrap.Modal(deleteProjectModal);
        }

        // Initialize test suite delete modals
        {% for test_suite in test_suites %}
        {% if test_suite.active %}
        var deleteTestSuiteModal{{ test_suite.id }} = document.getElementById('deleteTestSuiteModal{{ test_suite.id }}')
        if (deleteTestSuiteModal{{ test_suite.id }}) {
            new bootstrap.Modal(deleteTestSuiteModal{{ test_suite.id }});
        }
        {% endif %}
        {% endfor %}
    });
</script>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Project Details</h5>
                <div>
                    <a href="{{ url_for('edit_project', project_id=project.id) }}" class="btn btn-sm btn-primary me-2">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                    <a href="{{ url_for('create_test_suite', project_id=project.id) }}" class="btn btn-sm btn-success me-2">
                        <i class="bi bi-plus-circle"></i> New Test Suite
                    </a>
                    <button type="button"
                            class="btn btn-sm btn-danger"
                            data-bs-toggle="modal"
                            data-bs-target="#deleteProjectModal">
                        <i class="bi bi-trash"></i> Delete Project
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Name:</strong> {{ project.name }}</p>
                        <p><strong>Path:</strong> {{ project.path }}</p>
                        <p><strong>Created:</strong> {{ project.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                        <p><strong>Last Updated:</strong> {{ project.updated_at.strftime('%Y-%m-%d %H:%M') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Description:</strong></p>
                        <p>{{ project.description or 'No description provided.' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Suites</h5>
            </div>
            <div class="card-body">
                {% if test_suites %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Test Cases</th>
                                <th>Test Runs</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test_suite in test_suites %}
                            {% if test_suite.active %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('test_suite_detail', suite_id=test_suite.id) }}">
                                        {{ test_suite.name }}
                                    </a>
                                </td>
                                <td>{{ test_suite.description|truncate(50) or 'No description' }}</td>
                                <td>{{ test_suite.test_cases|selectattr('active', 'equalto', true)|list|length }}</td>
                                <td>{{ test_suite.test_runs|selectattr('active', 'equalto', true)|list|length }}</td>
                                <td>{{ test_suite.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('test_suite_detail', suite_id=test_suite.id) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{{ url_for('edit_test_suite', suite_id=test_suite.id) }}" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-success"
                                                data-bs-toggle="tooltip"
                                                title="View Test Cases"
                                                onclick="openTestCasesModal('{{ test_suite.id }}', '{{ test_suite.name }}')">
                                            <i class="bi bi-play-fill"></i>
                                        </button>
                                        <button type="button"
                                                class="btn btn-sm btn-danger"
                                                data-bs-toggle="modal"
                                                data-bs-target="#deleteTestSuiteModal{{ test_suite.id }}">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Test Suite Modal -->
                                    <div class="modal fade" id="deleteTestSuiteModal{{ test_suite.id }}" tabindex="-1">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content">
                                                <div class="modal-header border-bottom-0">
                                                    <h5 class="modal-title">Delete Test Suite</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                </div>
                                                <div class="modal-body text-center py-4">
                                                    <i class="bi bi-exclamation-triangle text-warning display-4 mb-3"></i>
                                                    <p class="mb-0">Are you sure you want to delete <strong>{{ test_suite.name }}</strong>?</p>
                                                    <p class="text-muted small">This action cannot be undone and will delete all associated test cases and runs.</p>
                                                </div>
                                                <div class="modal-footer border-top-0">
                                                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                                                    <form action="{{ url_for('delete_test_suite', suite_id=test_suite.id) }}" method="POST" class="d-inline">
                                                        <button type="submit" class="btn btn-danger">Delete Test Suite</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test suites found for this project.
                    <a href="{{ url_for('create_test_suite', project_id=project.id) }}" class="alert-link">Create a new test suite</a>.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Test Cases Section -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test Cases</h5>
            </div>
            <div class="card-body">
                {% set has_test_cases = false %}
                {% for test_suite in test_suites %}
                    {% if test_suite.active and test_suite.test_cases|selectattr('active', 'equalto', true)|list|length > 0 %}
                        {% set has_test_cases = true %}
                    {% endif %}
                {% endfor %}

                {% if has_test_cases %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Test Case</th>
                                <th>Test Suite</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test_suite in test_suites %}
                                {% if test_suite.active %}
                                    {% for test_case in test_suite.test_cases %}
                                        {% if test_case.active %}
                                        <tr>
                                            <td>{{ test_case.name }}</td>
                                            <td>
                                                <a href="{{ url_for('test_suite_detail', suite_id=test_suite.id) }}">
                                                    {{ test_suite.name }}
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if test_case.status == 'passed' else 'warning' if test_case.status == 'generated' else 'danger' if test_case.status == 'failed' else 'secondary' }}">
                                                    {{ test_case.status }}
                                                </span>
                                            </td>
                                            <td>{{ test_case.created_at.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ url_for('test_case_detail', case_id=test_case.id) }}" class="btn btn-sm btn-info">
                                                        <i class="bi bi-eye"></i> View
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-success"
                                                            data-bs-toggle="tooltip"
                                                            title="View Test Cases"
                                                            onclick="openTestCasesModal('{{ test_suite.id }}', '{{ test_suite.name }}')">
                                                        <i class="bi bi-play-fill"></i> Run
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endif %}
                                    {% endfor %}
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> No test cases found for this project.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if test_suites and test_stats %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Test Runs</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Test Suite</th>
                                <th>Run Name</th>
                                <th>Status</th>
                                <th>Results</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for test_suite in test_suites %}
                            {% if test_suite.active %}
                                {% for test_run in test_suite.test_runs %}
                                {% if test_run.active %}
                                <tr>
                                    <td>{{ test_suite.name }}</td>
                                    <td>
                                        <a href="{{ url_for('test_run_detail', run_id=test_run.id) }}">
                                            {{ test_run.name }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if test_run.status == 'completed' else 'warning' if test_run.status == 'running' else 'danger' if test_run.status == 'failed' else 'secondary' }}">
                                            {{ test_run.status }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if test_run.id in test_stats %}
                                        <div class="d-flex align-items-center">
                                            <div class="progress flex-grow-1" style="height: 10px;">
                                                {% set stats = test_stats[test_run.id] %}
                                                {% set total = stats.total %}
                                                {% if total > 0 %}
                                                    {% set passed_percent = (stats.passed / total * 100)|round|int %}
                                                    {% set failed_percent = (stats.failed / total * 100)|round|int %}
                                                    {% set error_percent = (stats.error / total * 100)|round|int %}
                                                    {% set skipped_percent = (stats.skipped / total * 100)|round|int %}

                                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ passed_percent }}%" title="Passed: {{ stats.passed }}"></div>
                                                    <div class="progress-bar bg-danger" role="progressbar" style="width: {{ failed_percent }}%" title="Failed: {{ stats.failed }}"></div>
                                                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ error_percent }}%" title="Error: {{ stats.error }}"></div>
                                                    <div class="progress-bar bg-secondary" role="progressbar" style="width: {{ skipped_percent }}%" title="Skipped: {{ stats.skipped }}"></div>
                                                {% endif %}
                                            </div>
                                            <span class="ms-2">
                                                {{ stats.passed }}/{{ stats.total }}
                                            </span>
                                        </div>
                                        {% else %}
                                        <span class="text-muted">No results</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ test_run.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    <td>
                                        <a href="{{ url_for('test_run_detail', run_id=test_run.id) }}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}


<!-- Delete Project Modal -->
<div class="modal fade" id="deleteProjectModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Delete Project</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center py-4">
                <i class="bi bi-exclamation-triangle text-warning display-4 mb-3"></i>
                <p class="mb-0">Are you sure you want to delete <strong>{{ project.name }}</strong>?</p>
                <p class="text-muted small">This action cannot be undone and will delete all associated test suites, test cases, and runs.</p>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('delete_project', project_id=project.id) }}" method="POST" class="d-inline">
                    <button type="submit" class="btn btn-danger">Delete Project</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}